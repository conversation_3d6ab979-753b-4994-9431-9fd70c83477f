version: '3.8'

services:
  # Init container to copy existing milvus data to volume
  milvus-init:
    build: .
    volumes:
      - milvus_data:/milvus_volume
      - ./data/milvus:/source_data:ro
    command: >
      bash -c "
        echo 'Checking if milvus volume needs initialization...' &&
        if [ ! -f /milvus_volume/milvus_lite.db ] && [ -f /source_data/milvus_lite.db ]; then
          echo 'Copying existing milvus database to volume...' &&
          cp -r /source_data/* /milvus_volume/ &&
          echo 'Database copied successfully'
        else
          echo 'Volume already initialized or no source database found'
        fi &&
        echo 'Milvus volume initialization complete'
      "

  api:
    build: .
    ports:
      - '8000:8000'
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - milvus-init
    volumes:
      - .:/app
      - milvus_data:/app/data/milvus:rw
      - ./data/TMDB_all_movies.csv:/app/data/TMDB_all_movies.csv:ro
    user: root
    command: >
      bash -c "
        /usr/local/bin/init-milvus.sh &&
        groupadd -g 1000 appuser 2>/dev/null || true &&
        useradd -u 1000 -g appuser -m appuser 2>/dev/null || true &&
        chown -R appuser:appuser /app/data/milvus &&
        su -c 'cd /app && pip install --no-cache-dir -r requirements.txt && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload' - appuser
      "

  redis:
    image: redis:7-alpine
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data

volumes:
  redis_data:
  milvus_data:
