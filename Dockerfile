FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libomp-dev \
    curl \
    wget \
    git \
    cmake \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Copy and make initialization script executable
COPY scripts/init-milvus.sh /usr/local/bin/init-milvus.sh
RUN chmod +x /usr/local/bin/init-milvus.sh

# Create data directory and ensure proper permissions
RUN mkdir -p /app/data/milvus && \
    chmod 755 /app/data/milvus

# Create volume mount point for Milvus data
VOLUME ["/app/data/milvus"]

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
