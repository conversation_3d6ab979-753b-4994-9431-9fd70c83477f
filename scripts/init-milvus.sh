#!/bin/bash

# Initialize Milvus data directory
# This script ensures that the Milvus data directory is properly set up
# with correct permissions and structure

set -e

MILVUS_DATA_DIR="/app/data/milvus"

echo "Initializing Milvus data directory..."

# Create the directory if it doesn't exist
mkdir -p "$MILVUS_DATA_DIR"

# Set proper permissions only for the milvus directory
chmod 755 "$MILVUS_DATA_DIR"

# Create a .keep file to ensure the directory is preserved in git
touch "$MILVUS_DATA_DIR/.keep"

echo "Milvus data directory initialized successfully at $MILVUS_DATA_DIR"

# List directory contents for verification
ls -la "$MILVUS_DATA_DIR" || echo "Directory listing failed, but directory exists"
