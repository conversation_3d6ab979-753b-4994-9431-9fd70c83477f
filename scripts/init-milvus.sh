#!/bin/bash

# Initialize Milvus data directory
# This script ensures that the Milvus data directory is properly set up
# with correct permissions and structure

set -e

MILVUS_DATA_DIR="/app/data/milvus"

echo "Initializing Milvus data directory..."

# Create the directory if it doesn't exist
mkdir -p "$MILVUS_DATA_DIR"

# Set proper permissions
chmod 755 "$MILVUS_DATA_DIR"

# Create a .keep file to ensure the directory is preserved in git
touch "$MILVUS_DATA_DIR/.keep"

# If running as root, ensure the directory is owned by the app user
if [ "$(id -u)" = "0" ]; then
    # Check if appuser exists, create if not
    if ! id -u appuser >/dev/null 2>&1; then
        groupadd -g 1000 appuser 2>/dev/null || true
        useradd -u 1000 -g appuser -m appuser 2>/dev/null || true
    fi
    
    # Change ownership to appuser
    chown -R appuser:appuser "$MILVUS_DATA_DIR"
fi

echo "Milvus data directory initialized successfully at $MILVUS_DATA_DIR"

# List directory contents for verification
ls -la "$MILVUS_DATA_DIR"
