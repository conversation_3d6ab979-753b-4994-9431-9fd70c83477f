# Docker Milvus Setup

Этот документ описывает настройку Docker volume для базы данных Milvus Lite в проекте moviebox-api.

## Обзор

Проект использует Milvus Lite для хранения векторных эмбеддингов фильмов. База данных Milvus Lite сохраняется в файле `milvus_lite.db` в директории `data/milvus/`.

## Docker Volume Configuration

### Dockerfile

В `Dockerfile` настроены следующие элементы:

1. **Создание директории данных**: 
   ```dockerfile
   RUN mkdir -p /app/data/milvus && \
       chmod -R 755 /app/data
   ```

2. **Скрипт инициализации**:
   ```dockerfile
   COPY scripts/init-milvus.sh /usr/local/bin/init-milvus.sh
   RUN chmod +x /usr/local/bin/init-milvus.sh
   ```

3. **Volume mount point**:
   ```dockerfile
   VOLUME ["/app/data/milvus"]
   ```

### docker-compose.yml

В `docker-compose.yml` настроен именованный volume:

```yaml
volumes:
  - milvus_data:/app/data/milvus:rw

volumes:
  milvus_data:
```

## Как это работает

1. **При сборке образа**: 
   - Создается структура директорий
   - Копируется скрипт инициализации
   - Устанавливаются правильные разрешения

2. **При запуске контейнера**:
   - Выполняется скрипт `init-milvus.sh`
   - Создается пользователь `appuser` с правильными разрешениями
   - Директория `/app/data/milvus` монтируется как Docker volume

3. **При первом запуске приложения**:
   - Milvus Lite автоматически создает файл `milvus_lite.db`
   - Файл сохраняется в Docker volume и персистентен между перезапусками

## Преимущества такой настройки

1. **Персистентность данных**: База данных сохраняется между перезапусками контейнеров
2. **Изоляция**: Данные изолированы от кода приложения
3. **Производительность**: Docker volume обеспечивает оптимальную производительность I/O
4. **Безопасность**: Правильные разрешения файловой системы

## Управление данными

### Просмотр volume
```bash
docker volume ls
docker volume inspect moviebox-api_milvus_data
```

### Очистка данных
```bash
# Остановить контейнеры
docker-compose down

# Удалить volume (ВНИМАНИЕ: это удалит все данные!)
docker volume rm moviebox-api_milvus_data

# Перезапустить
docker-compose up --build
```

### Резервное копирование
```bash
# Создать резервную копию volume
docker run --rm -v moviebox-api_milvus_data:/data -v $(pwd):/backup alpine tar czf /backup/milvus_backup.tar.gz -C /data .

# Восстановить из резервной копии
docker run --rm -v moviebox-api_milvus_data:/data -v $(pwd):/backup alpine tar xzf /backup/milvus_backup.tar.gz -C /data
```

## Troubleshooting

### Проблемы с разрешениями
Если возникают проблемы с разрешениями, проверьте:
```bash
docker-compose exec api ls -la /app/data/milvus/
```

### Проверка volume
```bash
docker-compose exec api df -h /app/data/milvus
```

### Логи инициализации
```bash
docker-compose logs api | grep -i milvus
```
